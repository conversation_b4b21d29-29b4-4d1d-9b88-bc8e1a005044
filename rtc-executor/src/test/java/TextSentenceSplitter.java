	import java.util.ArrayList;
	import java.util.List;
	import java.util.regex.Matcher;
	import java.util.regex.Pattern;
	public class TextSentenceSplitter {
	    // 定义中文和英文标点符号的正则表达式
	    private static final String PUNCTUATION_REGEX = "，。！？";
	    private static final Pattern PUNCTUATION_PATTERN = Pattern.compile(PUNCTUATION_REGEX);
	    /**
	     * 将文本拆分成句子
	     * @param text 输入文本
	     * @return 拆分后的句子列表
	     */
	    public static List<String> splitText(String text) {
	        List<String> sentences = new ArrayList<>();
	        if (text == null || text.isEmpty()) {
	            return sentences;
	        }
	        // 获取所有标点符号的位置
	        List<Integer> punctuationPositions = findPunctuationPositions(text);
	        int start = 0;
	        int textLength = text.length();
	        while (start < textLength) {
	            // 计算当前句子的最大结束位置
	            int end = Math.min(start + 100, textLength);
	            // 如果剩余文本不足10个汉字，直接添加并结束
	            if (end - start < 10) {
	                sentences.add(text.substring(start));
	                break;
	            }
	            // 查找在最大长度内的最后一个标点符号位置
	            int lastPunctuationPos = findLastPunctuationBefore(punctuationPositions, end);
	            // 如果找到了标点符号，并且从开始到标点符号的位置不小于10个汉字
	            if (lastPunctuationPos >= start + 10) {
	                sentences.add(text.substring(start, lastPunctuationPos + 1));
	                start = lastPunctuationPos + 1;
	            } else {
	                // 如果没有找到合适的标点符号，则强制在最大长度处拆分
	                // 但要确保拆分后的部分不小于10个汉字
	                if (end - start >= 10) {
	                    sentences.add(text.substring(start, end));
	                    start = end;
	                } else {
	                    // 如果剩余部分不足10个汉字，则与下一部分合并
	                    end = Math.min(start + 100, textLength);
	                    sentences.add(text.substring(start, end));
	                    start = end;
	                }
	            }
	        }
	        return sentences;
	    }
	    /**
	     * 查找文本中所有标点符号的位置
	     * @param text 输入文本
	     * @return 标点符号位置列表
	     */
	    private static List<Integer> findPunctuationPositions(String text) {
	        List<Integer> positions = new ArrayList<>();
	        Matcher matcher = PUNCTUATION_PATTERN.matcher(text);
	        while (matcher.find()) {
	            positions.add(matcher.start());
	        }
	        return positions;
	    }
	    /**
	     * 查找在指定位置之前的最后一个标点符号位置
	     * @param punctuationPositions 标点符号位置列表
	     * @param pos 指定位置
	     * @return 最后一个标点符号位置，如果没有则返回-1
	     */
	    private static int findLastPunctuationBefore(List<Integer> punctuationPositions, int pos) {
	        int lastPos = -1;
	        for (int p : punctuationPositions) {
	            if (p < pos) {
	                lastPos = p;
	            } else {
	                break;
	            }
	        }
	        return lastPos;
	    }
	    public static void main(String[] args) {
	        String text = "这是一个测试文本，用于演示文本拆句功能。我们希望将长文本拆分成多个短句，每个短句的最大长度为400个汉字，最小长度为10个汉字。拆句时应尽量在标点符号处进行，以保证语义的完整性。如果两个标点符号之间的内容超过400个汉字，则需要强制拆分。如果两个标点符号之间的内容超过400个汉字，则需要强制拆分。";
	        List<String> sentences = splitText(text);
	        System.out.println("拆分后的句子：");
	        for (int i = 0; i < sentences.size(); i++) {
	            System.out.println("句子 " + (i + 1) + ": " + sentences.get(i));
	            System.out.println("长度: " + sentences.get(i).length());
	        }
	    }
	}